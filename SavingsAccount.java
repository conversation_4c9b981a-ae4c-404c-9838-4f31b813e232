import java.time.LocalDate;

/**
 * Savings Account class that extends BankAccount
 * Provides interest calculation functionality
 */
public class SavingsAccount extends BankAccount {
    private static final double INTEREST_RATE = 0.04; // 4% annual interest
    
    // Constructor with guardian (for minors)
    public SavingsAccount(String accountNumber, String fullName, String gender, LocalDate dob, 
                         String guardianName, String guardianAadhar, Address address, 
                         String password, String pin, String securityQuestion, String securityAnswer) {
        super(accountNumber, fullName, gender, dob, guardianName, guardianAadhar, address, 
              "Savings", password, pin, securityQuestion, securityAnswer);
    }
    
    // Constructor without guardian (for adults)
    public SavingsAccount(String accountNumber, String fullName, String gender, LocalDate dob, 
                         Address address, String password, String pin, 
                         String securityQuestion, String securityAnswer) {
        super(accountNumber, fullName, gender, dob, address, "Savings", password, pin, 
              securityQuestion, securityAnswer);
    }
    
    /**
     * Apply interest to the savings account
     * Interest is calculated annually at 4%
     */
    public void applyInterest() {
        double currentBalance = getBalance();
        if (currentBalance <= 0) {
            System.out.println("No balance available for interest calculation.");
            return;
        }
        
        double interestAmount = currentBalance * INTEREST_RATE;
        deposit(interestAmount, "Annual Interest Applied @ " + (INTEREST_RATE * 100) + "%");
        
        System.out.println("Interest applied successfully!");
        System.out.println("Interest Amount: ₹" + String.format("%.2f", interestAmount));
        System.out.println("New Balance: ₹" + String.format("%.2f", getBalance()));
    }
    
    /**
     * Override withdraw method to add minimum balance check for savings account
     */
    @Override
    public boolean withdraw(double amount, String description) {
        double currentBalance = getBalance();
        double minimumBalance = 1000.0; // Minimum balance requirement for savings account
        
        if (amount <= 0) {
            System.out.println("Invalid amount. Please enter a positive value.");
            return false;
        }
        
        if (currentBalance - amount < minimumBalance) {
            System.out.println("Withdrawal denied. Minimum balance of ₹" + minimumBalance + " must be maintained.");
            System.out.println("Available for withdrawal: ₹" + (currentBalance - minimumBalance));
            return false;
        }
        
        return super.withdraw(amount, description);
    }
    
    /**
     * Override withdraw method without description
     */
    @Override
    public boolean withdraw(double amount) {
        return withdraw(amount, "Cash Withdrawal");
    }
    
    /**
     * Get interest rate for this savings account
     */
    public double getInterestRate() {
        return INTEREST_RATE;
    }
    
    /**
     * Calculate potential interest for current balance
     */
    public double calculatePotentialInterest() {
        return getBalance() * INTEREST_RATE;
    }

    // Transaction limits for savings account
    private static final double DAILY_WITHDRAWAL_LIMIT = 50000.0; // ₹50,000 per day
    private static final double MONTHLY_WITHDRAWAL_LIMIT = 200000.0; // ₹2,00,000 per month
    private static final double DAILY_DEPOSIT_LIMIT = 100000.0; // ₹1,00,000 per day
    private static final double MONTHLY_DEPOSIT_LIMIT = 500000.0; // ₹5,00,000 per month

    /**
     * Override transaction limit checking for savings account
     */
    @Override
    protected boolean checkTransactionLimits(double amount, String transactionType) {
        if (transactionType.equals("WITHDRAWAL")) {
            // Check daily withdrawal limit
            if (getDailyTransactionAmount() + amount > DAILY_WITHDRAWAL_LIMIT) {
                double remainingDaily = DAILY_WITHDRAWAL_LIMIT - getDailyTransactionAmount();
                System.out.println("❌ Daily withdrawal limit exceeded!");
                System.out.println("Daily limit: ₹" + DAILY_WITHDRAWAL_LIMIT);
                System.out.println("Already withdrawn today: ₹" + getDailyTransactionAmount());
                System.out.println("Remaining daily limit: ₹" + Math.max(0, remainingDaily));
                return false;
            }

            // Check monthly withdrawal limit
            if (getMonthlyTransactionAmount() + amount > MONTHLY_WITHDRAWAL_LIMIT) {
                double remainingMonthly = MONTHLY_WITHDRAWAL_LIMIT - getMonthlyTransactionAmount();
                System.out.println("❌ Monthly withdrawal limit exceeded!");
                System.out.println("Monthly limit: ₹" + MONTHLY_WITHDRAWAL_LIMIT);
                System.out.println("Already withdrawn this month: ₹" + getMonthlyTransactionAmount());
                System.out.println("Remaining monthly limit: ₹" + Math.max(0, remainingMonthly));
                return false;
            }
        } else if (transactionType.equals("DEPOSIT")) {
            // Check daily deposit limit
            if (getDailyTransactionAmount() + amount > DAILY_DEPOSIT_LIMIT) {
                double remainingDaily = DAILY_DEPOSIT_LIMIT - getDailyTransactionAmount();
                System.out.println("❌ Daily deposit limit exceeded!");
                System.out.println("Daily limit: ₹" + DAILY_DEPOSIT_LIMIT);
                System.out.println("Already deposited today: ₹" + getDailyTransactionAmount());
                System.out.println("Remaining daily limit: ₹" + Math.max(0, remainingDaily));
                return false;
            }

            // Check monthly deposit limit
            if (getMonthlyTransactionAmount() + amount > MONTHLY_DEPOSIT_LIMIT) {
                double remainingMonthly = MONTHLY_DEPOSIT_LIMIT - getMonthlyTransactionAmount();
                System.out.println("❌ Monthly deposit limit exceeded!");
                System.out.println("Monthly limit: ₹" + MONTHLY_DEPOSIT_LIMIT);
                System.out.println("Already deposited this month: ₹" + getMonthlyTransactionAmount());
                System.out.println("Remaining monthly limit: ₹" + Math.max(0, remainingMonthly));
                return false;
            }
        }

        return true; // Transaction is within limits
    }
    
    /**
     * Override viewAccountDetails to show savings-specific information
     */
    @Override
    public void viewAccountDetails() {
        super.viewAccountDetails();
        System.out.println("Interest Rate: " + (INTEREST_RATE * 100) + "% per annum");
        System.out.println("Minimum Balance: ₹1000");
        System.out.println("Potential Annual Interest: ₹" + String.format("%.2f", calculatePotentialInterest()));

        // Show transaction limits and usage
        System.out.println("\n--- Transaction Limits ---");
        System.out.println("Daily Withdrawal Limit: ₹" + DAILY_WITHDRAWAL_LIMIT);
        System.out.println("Monthly Withdrawal Limit: ₹" + MONTHLY_WITHDRAWAL_LIMIT);
        System.out.println("Daily Deposit Limit: ₹" + DAILY_DEPOSIT_LIMIT);
        System.out.println("Monthly Deposit Limit: ₹" + MONTHLY_DEPOSIT_LIMIT);

        System.out.println("\n--- Today's Usage ---");
        System.out.println("Transactions today: ₹" + getDailyTransactionAmount());
        System.out.println("Remaining daily limit: ₹" + Math.max(0, Math.min(DAILY_WITHDRAWAL_LIMIT, DAILY_DEPOSIT_LIMIT) - getDailyTransactionAmount()));

        System.out.println("\n--- This Month's Usage ---");
        System.out.println("Transactions this month: ₹" + getMonthlyTransactionAmount());
        System.out.println("Remaining monthly limit: ₹" + Math.max(0, Math.min(MONTHLY_WITHDRAWAL_LIMIT, MONTHLY_DEPOSIT_LIMIT) - getMonthlyTransactionAmount()));
    }

    /**
     * Get transaction limits information
     */
    public void showTransactionLimits() {
        System.out.println("\n=== Savings Account Transaction Limits ===");
        System.out.println("Daily Withdrawal Limit: ₹" + DAILY_WITHDRAWAL_LIMIT);
        System.out.println("Monthly Withdrawal Limit: ₹" + MONTHLY_WITHDRAWAL_LIMIT);
        System.out.println("Daily Deposit Limit: ₹" + DAILY_DEPOSIT_LIMIT);
        System.out.println("Monthly Deposit Limit: ₹" + MONTHLY_DEPOSIT_LIMIT);

        double dailyRemaining = Math.min(DAILY_WITHDRAWAL_LIMIT, DAILY_DEPOSIT_LIMIT) - getDailyTransactionAmount();
        double monthlyRemaining = Math.min(MONTHLY_WITHDRAWAL_LIMIT, MONTHLY_DEPOSIT_LIMIT) - getMonthlyTransactionAmount();

        System.out.println("\nToday's remaining limit: ₹" + Math.max(0, dailyRemaining));
        System.out.println("This month's remaining limit: ₹" + Math.max(0, monthlyRemaining));
    }
}
