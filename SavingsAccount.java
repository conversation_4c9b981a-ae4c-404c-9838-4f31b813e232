import java.time.LocalDate;

/**
 * Savings Account class that extends BankAccount
 * Provides interest calculation functionality
 */
public class SavingsAccount extends BankAccount {
    private static final double INTEREST_RATE = 0.04; // 4% annual interest
    
    // Constructor with guardian (for minors)
    public SavingsAccount(String accountNumber, String fullName, String gender, LocalDate dob, 
                         String guardianName, String guardianAadhar, Address address, 
                         String password, String pin, String securityQuestion, String securityAnswer) {
        super(accountNumber, fullName, gender, dob, guardianName, guardianAadhar, address, 
              "Savings", password, pin, securityQuestion, securityAnswer);
    }
    
    // Constructor without guardian (for adults)
    public SavingsAccount(String accountNumber, String fullName, String gender, LocalDate dob, 
                         Address address, String password, String pin, 
                         String securityQuestion, String securityAnswer) {
        super(accountNumber, fullName, gender, dob, address, "Savings", password, pin, 
              securityQuestion, securityAnswer);
    }
    
    /**
     * Apply interest to the savings account
     * Interest is calculated annually at 4%
     */
    public void applyInterest() {
        double currentBalance = getBalance();
        if (currentBalance <= 0) {
            System.out.println("No balance available for interest calculation.");
            return;
        }
        
        double interestAmount = currentBalance * INTEREST_RATE;
        deposit(interestAmount, "Annual Interest Applied @ " + (INTEREST_RATE * 100) + "%");
        
        System.out.println("Interest applied successfully!");
        System.out.println("Interest Amount: ₹" + String.format("%.2f", interestAmount));
        System.out.println("New Balance: ₹" + String.format("%.2f", getBalance()));
    }
    
    /**
     * Override withdraw method to add minimum balance check for savings account
     */
    @Override
    public boolean withdraw(double amount, String description) {
        double currentBalance = getBalance();
        double minimumBalance = 1000.0; // Minimum balance requirement for savings account
        
        if (amount <= 0) {
            System.out.println("Invalid amount. Please enter a positive value.");
            return false;
        }
        
        if (currentBalance - amount < minimumBalance) {
            System.out.println("Withdrawal denied. Minimum balance of ₹" + minimumBalance + " must be maintained.");
            System.out.println("Available for withdrawal: ₹" + (currentBalance - minimumBalance));
            return false;
        }
        
        return super.withdraw(amount, description);
    }
    
    /**
     * Override withdraw method without description
     */
    @Override
    public boolean withdraw(double amount) {
        return withdraw(amount, "Cash Withdrawal");
    }
    
    /**
     * Get interest rate for this savings account
     */
    public double getInterestRate() {
        return INTEREST_RATE;
    }
    
    /**
     * Calculate potential interest for current balance
     */
    public double calculatePotentialInterest() {
        return getBalance() * INTEREST_RATE;
    }
    
    /**
     * Override viewAccountDetails to show savings-specific information
     */
    @Override
    public void viewAccountDetails() {
        super.viewAccountDetails();
        System.out.println("Interest Rate: " + (INTEREST_RATE * 100) + "% per annum");
        System.out.println("Minimum Balance: ₹1000");
        System.out.println("Potential Annual Interest: ₹" + String.format("%.2f", calculatePotentialInterest()));
    }
}
