/**
 * Address class to store customer address information
 */
public class Address {
    private String street;
    private String city;
    private String state;
    private String pincode;
    
    public Address(String street, String city, String state, String pincode) {
        this.street = street;
        this.city = city;
        this.state = state;
        this.pincode = pincode;
    }
    
    // Getters
    public String getStreet() { return street; }
    public String getCity() { return city; }
    public String getState() { return state; }
    public String getPincode() { return pincode; }
    
    // Setters
    public void setStreet(String street) { this.street = street; }
    public void setCity(String city) { this.city = city; }
    public void setState(String state) { this.state = state; }
    public void setPincode(String pincode) { this.pincode = pincode; }
    
    /**
     * Validate pincode format (exactly 6 digits)
     */
    public static boolean isValidPincode(String pincode) {
        return pincode != null && pincode.matches("\\d{6}");
    }
    
    @Override
    public String toString() {
        return street + ", " + city + ", " + state + " - " + pincode;
    }
    
    /**
     * Get formatted address for display
     */
    public String getFormattedAddress() {
        StringBuilder sb = new StringBuilder();
        sb.append("Street: ").append(street).append("\n");
        sb.append("City: ").append(city).append("\n");
        sb.append("State: ").append(state).append("\n");
        sb.append("Pincode: ").append(pincode);
        return sb.toString();
    }
}
