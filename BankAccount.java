import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

/**
 * Abstract base class for all bank accounts
 */
public abstract class BankAccount {
    // Private fields for encapsulation
    private String accountNumber;
    private String fullName;
    private String gender;
    private LocalDate dob;
    private int age;
    private String guardianName;
    private String guardianAadhar;
    private Address address;
    private String accountType;
    private double balance;
    private String password;
    private String pin;
    private String securityQuestion;
    private String securityAnswer;
    private int failedLoginAttempts;
    private List<Transaction> transactionHistory;
    private boolean isLocked;
    private boolean isFrozen;
    private LocalDate lastTransactionDate;
    private double dailyTransactionAmount;
    private double monthlyTransactionAmount;
    private LocalDate currentMonth;

    // Constructor for accounts with guardian (under 18)
    public BankAccount(String accountNumber, String fullName, String gender, LocalDate dob, 
                      String guardianName, String guardianAadhar, Address address, 
                      String accountType, String password, String pin, 
                      String securityQuestion, String securityAnswer) {
        this.accountNumber = accountNumber;
        this.fullName = fullName;
        this.gender = gender;
        this.dob = dob;
        this.age = LocalDate.now().getYear() - dob.getYear();
        this.guardianName = guardianName;
        this.guardianAadhar = guardianAadhar;
        this.address = address;
        this.accountType = accountType;
        this.balance = 0.0;
        this.password = password;
        this.pin = pin;
        this.securityQuestion = securityQuestion;
        this.securityAnswer = securityAnswer;
        this.failedLoginAttempts = 0;
        this.transactionHistory = new ArrayList<>();
        this.isLocked = false;
        this.isFrozen = false;
        this.lastTransactionDate = LocalDate.now();
        this.dailyTransactionAmount = 0.0;
        this.monthlyTransactionAmount = 0.0;
        this.currentMonth = LocalDate.now().withDayOfMonth(1);
    }

    // Constructor for accounts without guardian (18+)
    public BankAccount(String accountNumber, String fullName, String gender, LocalDate dob, 
                      Address address, String accountType, String password, String pin, 
                      String securityQuestion, String securityAnswer) {
        this(accountNumber, fullName, gender, dob, null, null, address, accountType, 
             password, pin, securityQuestion, securityAnswer);
    }

    // Deposit method with overloading
    public void deposit(double amount) {
        deposit(amount, "Cash Deposit");
    }

    public void deposit(double amount, String description) {
        if (isFrozen) {
            System.out.println("⚠️  TRANSACTION DENIED ⚠️");
            System.out.println("Your account is frozen. Please contact the bank at 1800-NIKHIL-BANK.");
            return;
        }

        if (amount <= 0) {
            System.out.println("Invalid amount. Please enter a positive value.");
            return;
        }

        // Check transaction limits for deposits (if applicable)
        if (!checkTransactionLimits(amount, "DEPOSIT")) {
            return;
        }

        balance += amount;
        updateTransactionAmounts(amount);
        Transaction transaction = new Transaction("DEPOSIT", amount, balance, description);
        transactionHistory.add(transaction);
        System.out.println("Successfully deposited ₹" + amount + ". New balance: ₹" + balance);
    }

    // Withdraw method with overloading
    public boolean withdraw(double amount) {
        return withdraw(amount, "Cash Withdrawal");
    }

    public boolean withdraw(double amount, String description) {
        if (isFrozen) {
            System.out.println("⚠️  TRANSACTION DENIED ⚠️");
            System.out.println("Your account is frozen. Please contact the bank at 1800-NIKHIL-BANK.");
            return false;
        }

        if (amount <= 0) {
            System.out.println("Invalid amount. Please enter a positive value.");
            return false;
        }

        // Check transaction limits for withdrawals
        if (!checkTransactionLimits(amount, "WITHDRAWAL")) {
            return false;
        }

        if (amount > balance) {
            System.out.println("Insufficient balance. Available balance: ₹" + balance);
            return false;
        }

        balance -= amount;
        updateTransactionAmounts(amount);
        Transaction transaction = new Transaction("WITHDRAWAL", amount, balance, description);
        transactionHistory.add(transaction);
        System.out.println("Successfully withdrawn ₹" + amount + ". New balance: ₹" + balance);
        return true;
    }

    public double checkBalance() {
        return balance;
    }

    public boolean changePassword(String oldPassword, String newPassword) {
        if (!this.password.equals(oldPassword)) {
            System.out.println("Incorrect current password.");
            return false;
        }
        
        if (!isValidPassword(newPassword)) {
            System.out.println("Password must be at least 8 characters with 1 uppercase, 1 lowercase, 1 number, and 1 special character.");
            return false;
        }
        
        this.password = newPassword;
        Transaction transaction = new Transaction("PASSWORD_CHANGE", 0, balance, "Password changed successfully");
        transactionHistory.add(transaction);
        System.out.println("Password changed successfully.");
        return true;
    }

    public boolean changePin(String oldPin, String newPin) {
        if (!this.pin.equals(oldPin)) {
            System.out.println("Incorrect current PIN.");
            return false;
        }
        
        if (!isValidPin(newPin)) {
            System.out.println("PIN must be 4 digits and not related to your date of birth.");
            return false;
        }
        
        this.pin = newPin;
        Transaction transaction = new Transaction("PIN_CHANGE", 0, balance, "PIN changed successfully");
        transactionHistory.add(transaction);
        System.out.println("PIN changed successfully.");
        return true;
    }

    public void viewAccountDetails() {
        System.out.println("\n=== Account Details ===");
        System.out.println("Account Number: " + accountNumber);
        System.out.println("Full Name: " + fullName);
        System.out.println("Gender: " + gender);
        System.out.println("Date of Birth: " + dob.format(DateTimeFormatter.ofPattern("dd/MM/yyyy")));
        System.out.println("Age: " + age);
        if (guardianName != null) {
            System.out.println("Guardian Name: " + guardianName);
            System.out.println("Guardian Aadhaar: " + guardianAadhar);
        }
        System.out.println("Address: " + address.toString());
        System.out.println("Account Type: " + accountType);
        System.out.println("Current Balance: ₹" + balance);
        System.out.println("Account Status: " + (isLocked ? "LOCKED" : "ACTIVE"));
    }

    public void showLast5Transactions() {
        System.out.println("\n=== Last 5 Transactions ===");
        if (transactionHistory.isEmpty()) {
            System.out.println("No transactions found.");
            return;
        }
        
        int start = Math.max(0, transactionHistory.size() - 5);
        for (int i = transactionHistory.size() - 1; i >= start; i--) {
            System.out.println(transactionHistory.get(i));
        }
    }

    public void recordFailedLoginAttempt() {
        failedLoginAttempts++;
        Transaction transaction = new Transaction("FAILED_LOGIN", 0, balance, "Failed login attempt #" + failedLoginAttempts);
        transactionHistory.add(transaction);

        if (failedLoginAttempts >= 3 && failedLoginAttempts < 5) {
            isLocked = true;
            System.out.println("Your account has been locked due to multiple failed login attempts.");
            System.out.println("Please reset your password using the security question or contact customer service.");
        } else if (failedLoginAttempts >= 5) {
            isLocked = true;
            isFrozen = true;
            System.out.println("⚠️  ACCOUNT FROZEN ⚠️");
            System.out.println("Your account has been frozen due to excessive failed login attempts.");
            System.out.println("Please contact the bank immediately at 1800-NIKHIL-BANK for assistance.");
            System.out.println("Your account cannot be accessed until bank verification is completed.");
            Transaction freezeTransaction = new Transaction("ACCOUNT_FROZEN", 0, balance, "Account frozen due to security concerns");
            transactionHistory.add(freezeTransaction);
        }
    }

    public boolean resetPassword(String answer, String newPassword) {
        if (!this.securityAnswer.equalsIgnoreCase(answer)) {
            System.out.println("Incorrect security answer.");
            return false;
        }
        
        if (!isValidPassword(newPassword)) {
            System.out.println("Password must be at least 8 characters with 1 uppercase, 1 lowercase, 1 number, and 1 special character.");
            return false;
        }
        
        this.password = newPassword;
        this.failedLoginAttempts = 0;
        this.isLocked = false;
        Transaction transaction = new Transaction("PASSWORD_RESET", 0, balance, "Password reset via security question");
        transactionHistory.add(transaction);
        System.out.println("Password reset successfully. Account unlocked.");
        return true;
    }

    public boolean resetPin(String answer, String newPin) {
        if (!this.securityAnswer.equalsIgnoreCase(answer)) {
            System.out.println("Incorrect security answer.");
            return false;
        }
        
        if (!isValidPin(newPin)) {
            System.out.println("PIN must be 4 digits and not related to your date of birth.");
            return false;
        }
        
        this.pin = newPin;
        Transaction transaction = new Transaction("PIN_RESET", 0, balance, "PIN reset via security question");
        transactionHistory.add(transaction);
        System.out.println("PIN reset successfully.");
        return true;
    }

    // Validation methods
    public static boolean isValidPassword(String password) {
        if (password.length() < 8) return false;
        
        boolean hasUpper = false, hasLower = false, hasDigit = false, hasSpecial = false;
        
        for (char c : password.toCharArray()) {
            if (Character.isUpperCase(c)) hasUpper = true;
            else if (Character.isLowerCase(c)) hasLower = true;
            else if (Character.isDigit(c)) hasDigit = true;
            else if (!Character.isLetterOrDigit(c)) hasSpecial = true;
        }
        
        return hasUpper && hasLower && hasDigit && hasSpecial;
    }

    public boolean isValidPin(String pin) {
        if (pin.length() != 4 || !pin.matches("\\d{4}")) {
            return false;
        }
        
        // Check if PIN is related to DOB
        String dobStr = dob.format(DateTimeFormatter.ofPattern("ddMM"));
        String dobStr2 = dob.format(DateTimeFormatter.ofPattern("MMdd"));
        
        return !pin.equals(dobStr) && !pin.equals(dobStr2);
    }

    // Getters and Setters
    public String getAccountNumber() { return accountNumber; }
    public String getFullName() { return fullName; }
    public String getGender() { return gender; }
    public LocalDate getDob() { return dob; }
    public int getAge() { return age; }
    public String getGuardianName() { return guardianName; }
    public String getGuardianAadhar() { return guardianAadhar; }
    public Address getAddress() { return address; }
    public String getAccountType() { return accountType; }
    public double getBalance() { return balance; }
    public String getPassword() { return password; }
    public String getPin() { return pin; }
    public String getSecurityQuestion() { return securityQuestion; }
    public String getSecurityAnswer() { return securityAnswer; }
    public int getFailedLoginAttempts() { return failedLoginAttempts; }
    public boolean isLocked() { return isLocked; }
    public List<Transaction> getTransactionHistory() { return transactionHistory; }

    public void resetFailedLoginAttempts() {
        this.failedLoginAttempts = 0;
        this.isLocked = false;
    }

    // Transaction limit checking methods
    protected boolean checkTransactionLimits(double amount, String transactionType) {
        // Base class has no limits - to be overridden by subclasses
        return true;
    }

    protected void updateTransactionAmounts(double amount) {
        LocalDate today = LocalDate.now();
        LocalDate currentMonthStart = today.withDayOfMonth(1);

        // Reset daily amount if it's a new day
        if (!today.equals(lastTransactionDate)) {
            dailyTransactionAmount = 0.0;
            lastTransactionDate = today;
        }

        // Reset monthly amount if it's a new month
        if (!currentMonthStart.equals(currentMonth)) {
            monthlyTransactionAmount = 0.0;
            currentMonth = currentMonthStart;
        }

        // Update transaction amounts
        dailyTransactionAmount += amount;
        monthlyTransactionAmount += amount;
    }

    // Additional getters for new fields
    public boolean isFrozen() { return isFrozen; }
    public double getDailyTransactionAmount() { return dailyTransactionAmount; }
    public double getMonthlyTransactionAmount() { return monthlyTransactionAmount; }
    public LocalDate getLastTransactionDate() { return lastTransactionDate; }

    // Method to unfreeze account (for bank admin use)
    public void unfreezeAccount(String adminCode) {
        if ("ADMIN_UNFREEZE_2024".equals(adminCode)) {
            isFrozen = false;
            isLocked = false;
            failedLoginAttempts = 0;
            Transaction transaction = new Transaction("ACCOUNT_UNFROZEN", 0, balance, "Account unfrozen by bank administrator");
            transactionHistory.add(transaction);
            System.out.println("Account has been unfrozen by bank administrator.");
        } else {
            System.out.println("Invalid admin code. Account remains frozen.");
        }
    }
}
