import java.time.LocalDate;

/**
 * Test class to demonstrate enhanced banking system features:
 * - Transaction limits for savings accounts
 * - Account freeze after excessive failed logins
 * - Enhanced security messaging
 */
public class TestEnhancedFeatures {
    public static void main(String[] args) {
        System.out.println("=== Enhanced Banking System Features Test ===\n");
        
        // Create a savings account for testing
        Address address = new Address("123 Test Street", "Mumbai", "Maharashtra", "400001");
        SavingsAccount savingsAccount = new SavingsAccount(
            "**********", 
            "Test User", 
            "M", 
            LocalDate.of(1990, 8, 15),
            address,
            "Password123!",
            "5678",
            "What is your favorite color?",
            "Blue"
        );
        
        System.out.println("✅ Savings account created successfully!");
        savingsAccount.viewAccountDetails();
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // Test 1: Transaction Limits
        System.out.println("🔍 TEST 1: Transaction Limits for Savings Account");
        System.out.println("-".repeat(50));
        
        // Show transaction limits
        savingsAccount.showTransactionLimits();
        
        // Test normal deposits within limits
        System.out.println("\n📥 Testing normal deposits:");
        savingsAccount.deposit(25000, "Salary deposit");
        savingsAccount.deposit(15000, "Bonus deposit");
        
        // Test deposit exceeding daily limit
        System.out.println("\n📥 Testing deposit exceeding daily limit:");
        savingsAccount.deposit(70000, "Large deposit attempt"); // Should fail
        
        // Test normal withdrawals
        System.out.println("\n📤 Testing normal withdrawals:");
        savingsAccount.withdraw(10000, "ATM withdrawal");
        savingsAccount.withdraw(5000, "Online transfer");
        
        // Test withdrawal exceeding daily limit
        System.out.println("\n📤 Testing withdrawal exceeding daily limit:");
        savingsAccount.withdraw(40000, "Large withdrawal attempt"); // Should fail
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // Test 2: Failed Login Attempts and Account Freezing
        System.out.println("🔍 TEST 2: Failed Login Attempts and Account Freezing");
        System.out.println("-".repeat(50));
        
        // Create another account for testing login failures
        CurrentAccount testAccount = new CurrentAccount(
            "**********", 
            "Security Test User", 
            "F", 
            LocalDate.of(1985, 12, 10),
            address,
            "SecurePass456@",
            "9876",
            "What was your first pet?",
            "Fluffy"
        );
        
        System.out.println("✅ Test account created for security testing");
        
        // Simulate failed login attempts
        System.out.println("\n🔐 Simulating failed login attempts:");
        
        System.out.println("Attempt 1:");
        testAccount.recordFailedLoginAttempt();
        
        System.out.println("\nAttempt 2:");
        testAccount.recordFailedLoginAttempt();
        
        System.out.println("\nAttempt 3 (Account should be locked):");
        testAccount.recordFailedLoginAttempt();
        
        System.out.println("\nAttempt 4:");
        testAccount.recordFailedLoginAttempt();
        
        System.out.println("\nAttempt 5 (Account should be FROZEN):");
        testAccount.recordFailedLoginAttempt();
        
        // Test transaction attempts on frozen account
        System.out.println("\n💳 Testing transactions on frozen account:");
        testAccount.deposit(1000, "Deposit attempt on frozen account");
        testAccount.withdraw(500, "Withdrawal attempt on frozen account");
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // Test 3: Account Status Checking
        System.out.println("🔍 TEST 3: Account Status Checking");
        System.out.println("-".repeat(50));
        
        System.out.println("Savings Account Status:");
        System.out.println("- Locked: " + savingsAccount.isLocked());
        System.out.println("- Frozen: " + savingsAccount.isFrozen());
        System.out.println("- Failed Login Attempts: " + savingsAccount.getFailedLoginAttempts());
        
        System.out.println("\nTest Account Status:");
        System.out.println("- Locked: " + testAccount.isLocked());
        System.out.println("- Frozen: " + testAccount.isFrozen());
        System.out.println("- Failed Login Attempts: " + testAccount.getFailedLoginAttempts());
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // Test 4: Admin Account Unfreeze
        System.out.println("🔍 TEST 4: Admin Account Unfreeze");
        System.out.println("-".repeat(50));
        
        System.out.println("🔧 Attempting to unfreeze account with wrong admin code:");
        testAccount.unfreezeAccount("WRONG_CODE");
        
        System.out.println("\n🔧 Attempting to unfreeze account with correct admin code:");
        testAccount.unfreezeAccount("ADMIN_UNFREEZE_2024");
        
        System.out.println("\nAccount status after unfreeze:");
        System.out.println("- Locked: " + testAccount.isLocked());
        System.out.println("- Frozen: " + testAccount.isFrozen());
        System.out.println("- Failed Login Attempts: " + testAccount.getFailedLoginAttempts());
        
        // Test transaction after unfreeze
        System.out.println("\n💳 Testing transaction after unfreeze:");
        testAccount.deposit(2000, "Deposit after unfreeze");
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // Test 5: Transaction History with Security Events
        System.out.println("🔍 TEST 5: Transaction History with Security Events");
        System.out.println("-".repeat(50));
        
        System.out.println("Savings Account Transaction History:");
        savingsAccount.showLast5Transactions();
        
        System.out.println("\nTest Account Transaction History (including security events):");
        testAccount.showLast5Transactions();
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // Test 6: Updated Account Details View
        System.out.println("🔍 TEST 6: Enhanced Account Details View");
        System.out.println("-".repeat(50));
        
        System.out.println("Savings Account Details (with transaction limits):");
        savingsAccount.viewAccountDetails();
        
        System.out.println("\n=== Enhanced Features Test Completed Successfully! ===");
        System.out.println("\n🎯 Key Features Demonstrated:");
        System.out.println("✅ Transaction limits for savings accounts (daily/monthly)");
        System.out.println("✅ Account lockout after 3 failed login attempts");
        System.out.println("✅ Account freeze after 5 failed login attempts");
        System.out.println("✅ Enhanced security messaging");
        System.out.println("✅ Admin account unfreeze functionality");
        System.out.println("✅ Transaction blocking on frozen accounts");
        System.out.println("✅ Comprehensive transaction history with security events");
    }
}
