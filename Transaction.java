import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Transaction class to store transaction history
 */
public class Transaction {
    private String type;
    private double amount;
    private double balanceAfter;
    private LocalDateTime timestamp;
    private String description;
    
    public Transaction(String type, double amount, double balanceAfter, String description) {
        this.type = type;
        this.amount = amount;
        this.balanceAfter = balanceAfter;
        this.timestamp = LocalDateTime.now();
        this.description = description;
    }
    
    // Getters
    public String getType() { return type; }
    public double getAmount() { return amount; }
    public double getBalanceAfter() { return balanceAfter; }
    public LocalDateTime getTimestamp() { return timestamp; }
    public String getDescription() { return description; }
    
    @Override
    public String toString() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
        String formattedAmount = "";
        
        switch (type) {
            case "DEPOSIT":
                formattedAmount = "+₹" + String.format("%.2f", amount);
                break;
            case "WITHDRAWAL":
                formattedAmount = "-₹" + String.format("%.2f", amount);
                break;
            case "FAILED_LOGIN":
            case "PASSWORD_CHANGE":
            case "PIN_CHANGE":
            case "PASSWORD_RESET":
            case "PIN_RESET":
                formattedAmount = "N/A";
                break;
            default:
                formattedAmount = "₹" + String.format("%.2f", amount);
        }
        
        return String.format("[%s] %s | %s | Balance: ₹%.2f | %s", 
                           timestamp.format(formatter), 
                           type, 
                           formattedAmount, 
                           balanceAfter, 
                           description);
    }
    
    /**
     * Get a short summary of the transaction
     */
    public String getShortSummary() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM HH:mm");
        return String.format("%s - %s: ₹%.2f", 
                           timestamp.format(formatter), 
                           type, 
                           amount);
    }
}
