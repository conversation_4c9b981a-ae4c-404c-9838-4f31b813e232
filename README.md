# Banking System - Nikhil and Co

A comprehensive Java-based banking system with object-oriented design principles, featuring account creation, login, transactions, and security features.

## Features

### Main Menu Options
1. **Create Account** - Complete account creation flow
2. **Login to Account** - Secure login with account lockout
3. **Show Total Accounts Created** - Statistics display
4. **Exit** - Safe system exit

### Account Creation Flow
- **Personal Information**: Full name, gender-based greeting, date of birth validation
- **Age Verification**: Guardian information required for users under 18
- **Address Collection**: Street, city, state, and 6-digit pincode validation
- **Account Type Selection**: Savings or Current account
- **Unique Account Number**: Auto-generated with type-specific prefix
- **Security Setup**: Password validation, PIN creation, security question/answer

### Account Types

#### Savings Account (Account numbers start with 1)
- 4% annual interest rate
- Minimum balance requirement: ₹1000
- Interest application feature
- Inheritance from BankAccount class

#### Current Account (Account numbers start with 2)
- Overdraft facility: ₹10,000 default limit
- No minimum balance requirement
- Business-oriented features
- Overdraft tracking and management

### Security Features
- **Password Requirements**: Minimum 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special character
- **PIN Validation**: 4 digits, cannot be related to date of birth
- **Account Lockout**: Automatic lock after 3 failed login attempts
- **Account Freeze**: Automatic freeze after 5 failed login attempts with bank contact requirement
- **Security Questions**: For password/PIN reset functionality
- **Transaction History**: Complete audit trail with timestamps
- **Enhanced Security Messaging**: Clear warnings and contact information for frozen accounts

### Transaction Limits (Savings Accounts Only)
- **Daily Withdrawal Limit**: ₹50,000 per day
- **Monthly Withdrawal Limit**: ₹2,00,000 per month
- **Daily Deposit Limit**: ₹1,00,000 per day
- **Monthly Deposit Limit**: ₹5,00,000 per month
- **Real-time Limit Tracking**: Shows remaining limits and usage
- **Transaction Blocking**: Prevents transactions exceeding limits

### Account Menu (After Login)
1. **Deposit** - Add money to account (with transaction limits for savings)
2. **Withdraw** - Remove money (with balance/overdraft checks and limits)
3. **Check Balance** - View current balance and available funds
4. **Change Password** - Update account password
5. **Change PIN** - Update account PIN
6. **View Account Details** - Complete account information with transaction limits
7. **Show Last 5 Transactions** - Recent transaction history including security events
8. **Apply Interest** - Savings accounts only
9. **View Transaction Limits** - Savings accounts only - shows daily/monthly limits and usage
10. **Exit** - Return to main menu

## Object-Oriented Design

### Classes and Inheritance
- **BankAccount** (Abstract base class)
  - Common fields and methods for all account types
  - Abstract design for extensibility
- **SavingsAccount** extends BankAccount
  - Interest calculation and application
  - Minimum balance enforcement
- **CurrentAccount** extends BankAccount
  - Overdraft facility management
  - Business account features

### Supporting Classes
- **Address** - Customer address information with validation
- **Transaction** - Transaction history with timestamps
- **BankingSystem** - Main application controller

### OOP Principles Demonstrated
- **Encapsulation**: Private fields with public getters/setters
- **Inheritance**: SavingsAccount and CurrentAccount extend BankAccount
- **Polymorphism**: Method overriding and overloading
- **Abstraction**: Abstract BankAccount class

## Validation Features
- Date format validation (dd/mm/yyyy)
- Age verification and guardian requirements
- Pincode format validation (exactly 6 digits)
- Password strength validation
- PIN security validation (not related to DOB)
- Unique account number generation

## Transaction Management
- Complete transaction history with timestamps
- Failed login attempt tracking
- Balance tracking after each transaction
- Transaction categorization (DEPOSIT, WITHDRAWAL, etc.)

## How to Run

1. Compile all Java files:
   ```bash
   javac *.java
   ```

2. Run the banking system:
   ```bash
   java BankingSystem
   ```

3. Follow the on-screen prompts to:
   - Create new accounts
   - Login to existing accounts
   - Perform banking operations

## Sample Account Creation Flow

1. Enter full name (e.g., "Nikhil Sharma")
2. Select gender (M/F/Other) - receives personalized greeting
3. Enter date of birth (dd/mm/yyyy format)
4. If under 18, provide guardian information
5. Enter complete address with valid pincode
6. Choose account type (Savings/Current)
7. Create secure password and PIN
8. Set security question and answer
9. Receive unique account number

## Sample Login and Operations

1. Enter account number, password, and PIN
2. Access account menu with various operations
3. Perform deposits, withdrawals, balance checks
4. View transaction history and account details
5. Apply interest (Savings accounts only)

## Enhanced Security Features

### Account Protection Levels
1. **Normal Operation** - All transactions allowed within limits
2. **Account Locked** (3 failed logins) - Login blocked, password reset available
3. **Account Frozen** (5 failed logins) - All access blocked, bank contact required

### Transaction Monitoring
- Real-time limit checking for savings accounts
- Daily and monthly transaction tracking
- Automatic transaction blocking when limits exceeded
- Comprehensive audit trail with security events

### Administrative Features
- Admin account unfreeze functionality (code: ADMIN_UNFREEZE_2024)
- Security event logging in transaction history
- Enhanced status reporting and monitoring

## Error Handling
- Invalid input validation
- Account lockout after failed attempts
- Account freeze after excessive failed attempts
- Transaction limit enforcement for savings accounts
- Insufficient balance checks
- Overdraft limit enforcement
- Date validation and age verification
- Frozen account transaction blocking

## Testing
Run the enhanced features test to see all security features in action:
```bash
java TestEnhancedFeatures
```

This banking system demonstrates comprehensive Java programming concepts including OOP design, input validation, advanced security features, transaction monitoring, and user-friendly interface design.
