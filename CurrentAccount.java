import java.time.LocalDate;

/**
 * Current Account class that extends BankA<PERSON>unt
 * Provides overdraft facility and business-oriented features
 */
public class CurrentAccount extends BankAccount {
    private double overdraftLimit;
    private static final double DEFAULT_OVERDRAFT_LIMIT = 10000.0; // Default overdraft limit
    
    // Constructor with guardian (for minors)
    public CurrentAccount(String accountNumber, String fullName, String gender, LocalDate dob, 
                         String guardianName, String guardianAadhar, Address address, 
                         String password, String pin, String securityQuestion, String securityAnswer) {
        super(accountNumber, fullName, gender, dob, guardianName, guardianAadhar, address, 
              "Current", password, pin, securityQuestion, securityAnswer);
        this.overdraftLimit = DEFAULT_OVERDRAFT_LIMIT;
    }
    
    // Constructor without guardian (for adults)
    public CurrentAccount(String accountNumber, String fullName, String gender, LocalDate dob, 
                         Address address, String password, String pin, 
                         String securityQuestion, String securityAnswer) {
        super(accountNumber, fullName, gender, dob, address, "Current", password, pin, 
              securityQuestion, securityAnswer);
        this.overdraftLimit = DEFAULT_OVERDRAFT_LIMIT;
    }
    
    /**
     * Override withdraw method to allow overdraft facility
     */
    @Override
    public boolean withdraw(double amount, String description) {
        if (amount <= 0) {
            System.out.println("Invalid amount. Please enter a positive value.");
            return false;
        }
        
        double currentBalance = getBalance();
        double availableAmount = currentBalance + overdraftLimit;
        
        if (amount > availableAmount) {
            System.out.println("Withdrawal denied. Amount exceeds available balance + overdraft limit.");
            System.out.println("Available amount: ₹" + availableAmount);
            System.out.println("(Balance: ₹" + currentBalance + " + Overdraft: ₹" + overdraftLimit + ")");
            return false;
        }
        
        // Use parent's deposit/withdraw logic but bypass balance check
        double newBalance = currentBalance - amount;
        
        // Directly update balance through deposit with negative amount
        super.deposit(-amount, description.replace("Deposit", "Withdrawal"));
        
        if (newBalance < 0) {
            System.out.println("Overdraft used: ₹" + Math.abs(newBalance));
        }
        
        System.out.println("Successfully withdrawn ₹" + amount + ". New balance: ₹" + getBalance());
        return true;
    }
    
    /**
     * Override withdraw method without description
     */
    @Override
    public boolean withdraw(double amount) {
        return withdraw(amount, "Cash Withdrawal");
    }
    
    /**
     * Set overdraft limit (for bank admin use)
     */
    public void setOverdraftLimit(double limit) {
        if (limit < 0) {
            System.out.println("Overdraft limit cannot be negative.");
            return;
        }
        this.overdraftLimit = limit;
        System.out.println("Overdraft limit updated to ₹" + limit);
    }
    
    /**
     * Get current overdraft limit
     */
    public double getOverdraftLimit() {
        return overdraftLimit;
    }
    
    /**
     * Get available overdraft amount
     */
    public double getAvailableOverdraft() {
        double currentBalance = getBalance();
        if (currentBalance >= 0) {
            return overdraftLimit;
        } else {
            return Math.max(0, overdraftLimit + currentBalance);
        }
    }
    
    /**
     * Check if account is in overdraft
     */
    public boolean isInOverdraft() {
        return getBalance() < 0;
    }
    
    /**
     * Get overdraft amount being used
     */
    public double getOverdraftUsed() {
        double balance = getBalance();
        return balance < 0 ? Math.abs(balance) : 0;
    }
    
    /**
     * Override viewAccountDetails to show current account specific information
     */
    @Override
    public void viewAccountDetails() {
        super.viewAccountDetails();
        System.out.println("Overdraft Limit: ₹" + overdraftLimit);
        System.out.println("Available Overdraft: ₹" + getAvailableOverdraft());
        if (isInOverdraft()) {
            System.out.println("Overdraft Used: ₹" + getOverdraftUsed());
            System.out.println("Account Status: IN OVERDRAFT");
        }
        double totalAvailable = getBalance() + getAvailableOverdraft();
        System.out.println("Total Available Amount: ₹" + totalAvailable);
    }
    
    /**
     * Calculate overdraft interest (if implemented in future)
     */
    public double calculateOverdraftInterest() {
        if (!isInOverdraft()) {
            return 0;
        }
        
        double overdraftInterestRate = 0.12; // 12% annual interest on overdraft
        return getOverdraftUsed() * overdraftInterestRate;
    }
}
