import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

/**
 * Main Banking System class that handles all user interactions
 */
public class BankingSystem {
    private static Map<String, BankAccount> accounts = new HashMap<>();
    private static Scanner scanner = new Scanner(System.in);
    private static Random random = new Random();
    private static int totalAccountsCreated = 0;
    
    public static void main(String[] args) {
        System.out.println("=================================");
        System.out.println("    Welcome to Nikhil and Co     ");
        System.out.println("=================================");
        
        while (true) {
            showMainMenu();
            int choice = getIntInput("Enter your choice: ");
            
            switch (choice) {
                case 1:
                    createAccount();
                    break;
                case 2:
                    loginToAccount();
                    break;
                case 3:
                    showTotalAccounts();
                    break;
                case 4:
                    System.out.println("Thank you for using Nikhil and Co Banking System!");
                    System.exit(0);
                    break;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        }
    }
    
    private static void showMainMenu() {
        System.out.println("\n=== Main Menu ===");
        System.out.println("1. Create Account");
        System.out.println("2. Login to Account");
        System.out.println("3. Show Total Accounts Created");
        System.out.println("4. Exit");
    }
    
    private static void createAccount() {
        System.out.println("\n=== Account Creation ===");
        
        // Get full name
        System.out.print("Enter Full Name: ");
        String fullName = scanner.nextLine().trim();
        if (fullName.isEmpty()) {
            System.out.println("Name cannot be empty.");
            return;
        }
        
        // Get gender and greet accordingly
        String gender = getGender();
        String greeting = getGreeting(gender, fullName);
        System.out.println(greeting);
        
        // Get and validate date of birth
        LocalDate dob = getDateOfBirth();
        if (dob == null) return;
        
        int age = LocalDate.now().getYear() - dob.getYear();
        
        // Check if guardian is needed (under 18)
        String guardianName = null;
        String guardianAadhar = null;
        if (age < 18) {
            System.out.println("As you are under 18, we need guardian information.");
            System.out.print("Enter Guardian's Name: ");
            guardianName = scanner.nextLine().trim();
            
            guardianAadhar = getGuardianAadhar();
            if (guardianAadhar == null) return;
        }
        
        // Get address
        Address address = getAddress();
        if (address == null) return;
        
        // Get account type
        String accountType = getAccountType();
        if (accountType == null) return;
        
        // Generate unique account number
        String accountNumber = generateAccountNumber(accountType);
        
        // Get password
        String password = getValidPassword();
        if (password == null) return;
        
        // Get PIN
        String pin = getValidPin(dob);
        if (pin == null) return;
        
        // Get security question and answer
        String[] securityQA = getSecurityQuestionAnswer();
        if (securityQA == null) return;
        
        // Create account
        BankAccount account;
        if (accountType.equals("Savings")) {
            if (age < 18) {
                account = new SavingsAccount(accountNumber, fullName, gender, dob, 
                                           guardianName, guardianAadhar, address, 
                                           password, pin, securityQA[0], securityQA[1]);
            } else {
                account = new SavingsAccount(accountNumber, fullName, gender, dob, 
                                           address, password, pin, securityQA[0], securityQA[1]);
            }
        } else {
            if (age < 18) {
                account = new CurrentAccount(accountNumber, fullName, gender, dob, 
                                           guardianName, guardianAadhar, address, 
                                           password, pin, securityQA[0], securityQA[1]);
            } else {
                account = new CurrentAccount(accountNumber, fullName, gender, dob, 
                                           address, password, pin, securityQA[0], securityQA[1]);
            }
        }
        
        accounts.put(accountNumber, account);
        totalAccountsCreated++;
        
        System.out.println("\n🎉 Account created successfully! 🎉");
        System.out.println("Your Account Number: " + accountNumber);
        System.out.println("Account Type: " + accountType);
        System.out.println("Please remember your account number, password, and PIN for future logins.");
    }
    
    private static String getGender() {
        while (true) {
            System.out.print("Enter Gender (M/F/Other): ");
            String gender = scanner.nextLine().trim().toUpperCase();
            
            if (gender.equals("M") || gender.equals("F") || gender.equals("OTHER")) {
                return gender;
            }
            
            System.out.println("Invalid gender. Please enter M, F, or Other.");
        }
    }
    
    private static String getGreeting(String gender, String fullName) {
        String[] nameParts = fullName.split(" ");
        String firstName = nameParts[0];
        
        switch (gender) {
            case "M":
                return "Hello Mr. " + firstName + "!";
            case "F":
                return "Hello Ms. " + firstName + "!";
            default:
                return "Hello " + firstName + "!";
        }
    }
    
    private static LocalDate getDateOfBirth() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("dd/MM/yyyy");
        
        while (true) {
            System.out.print("Enter Date of Birth (dd/mm/yyyy): ");
            String dobStr = scanner.nextLine().trim();
            
            try {
                LocalDate dob = LocalDate.parse(dobStr, formatter);
                
                // Validate that the date is not in the future
                if (dob.isAfter(LocalDate.now())) {
                    System.out.println("Date of birth cannot be in the future.");
                    continue;
                }
                
                // Validate reasonable age (not more than 150 years old)
                if (dob.isBefore(LocalDate.now().minusYears(150))) {
                    System.out.println("Invalid date of birth. Please enter a valid date.");
                    continue;
                }
                
                return dob;
                
            } catch (DateTimeParseException e) {
                System.out.println("Invalid date format. Please use dd/mm/yyyy format (e.g., 15/08/1990).");
            }
        }
    }
    
    private static String getGuardianAadhar() {
        while (true) {
            System.out.print("Enter Guardian's Aadhaar Number (12 digits): ");
            String aadhar = scanner.nextLine().trim();
            
            if (aadhar.matches("\\d{12}")) {
                return aadhar;
            }
            
            System.out.println("Invalid Aadhaar number. Please enter exactly 12 digits.");
        }
    }
    
    private static Address getAddress() {
        System.out.println("\n--- Address Information ---");
        
        System.out.print("Enter Street: ");
        String street = scanner.nextLine().trim();
        if (street.isEmpty()) {
            System.out.println("Street cannot be empty.");
            return null;
        }
        
        System.out.print("Enter City: ");
        String city = scanner.nextLine().trim();
        if (city.isEmpty()) {
            System.out.println("City cannot be empty.");
            return null;
        }
        
        System.out.print("Enter State: ");
        String state = scanner.nextLine().trim();
        if (state.isEmpty()) {
            System.out.println("State cannot be empty.");
            return null;
        }
        
        String pincode = getValidPincode();
        if (pincode == null) return null;
        
        return new Address(street, city, state, pincode);
    }
    
    private static String getValidPincode() {
        while (true) {
            System.out.print("Enter Pincode (6 digits): ");
            String pincode = scanner.nextLine().trim();
            
            if (Address.isValidPincode(pincode)) {
                return pincode;
            }
            
            System.out.println("Invalid pincode. Please enter exactly 6 digits.");
        }
    }
    
    private static String getAccountType() {
        while (true) {
            System.out.println("\n--- Account Type ---");
            System.out.println("1. Savings");
            System.out.println("2. Current");
            
            int choice = getIntInput("Choose account type: ");
            
            switch (choice) {
                case 1:
                    return "Savings";
                case 2:
                    return "Current";
                default:
                    System.out.println("Invalid choice. Please select 1 or 2.");
            }
        }
    }
    
    private static String generateAccountNumber(String accountType) {
        String prefix = accountType.equals("Savings") ? "1" : "2";
        String accountNumber;
        
        do {
            // Generate 9 random digits after the prefix
            StringBuilder sb = new StringBuilder(prefix);
            for (int i = 0; i < 9; i++) {
                sb.append(random.nextInt(10));
            }
            accountNumber = sb.toString();
        } while (accounts.containsKey(accountNumber)); // Ensure uniqueness
        
        return accountNumber;
    }
    
    private static String getValidPassword() {
        while (true) {
            System.out.print("Create Password (min 8 chars, 1 upper, 1 lower, 1 number, 1 special): ");
            String password = scanner.nextLine();
            
            if (BankAccount.isValidPassword(password)) {
                return password;
            }
            
            System.out.println("Password must be at least 8 characters with 1 uppercase, 1 lowercase, 1 number, and 1 special character.");
        }
    }
    
    private static String getValidPin(LocalDate dob) {
        while (true) {
            System.out.print("Create 4-digit PIN: ");
            String pin = scanner.nextLine().trim();
            
            if (pin.length() != 4 || !pin.matches("\\d{4}")) {
                System.out.println("PIN must be exactly 4 digits.");
                continue;
            }
            
            // Check if PIN is related to DOB
            String dobStr = dob.format(DateTimeFormatter.ofPattern("ddMM"));
            String dobStr2 = dob.format(DateTimeFormatter.ofPattern("MMdd"));
            
            if (pin.equals(dobStr) || pin.equals(dobStr2)) {
                System.out.println("PIN cannot be related to your date of birth.");
                continue;
            }
            
            return pin;
        }
    }
    
    private static String[] getSecurityQuestionAnswer() {
        System.out.println("\n--- Security Question ---");
        System.out.print("Enter Security Question: ");
        String question = scanner.nextLine().trim();
        if (question.isEmpty()) {
            System.out.println("Security question cannot be empty.");
            return null;
        }
        
        System.out.print("Enter Security Answer: ");
        String answer = scanner.nextLine().trim();
        if (answer.isEmpty()) {
            System.out.println("Security answer cannot be empty.");
            return null;
        }
        
        return new String[]{question, answer};
    }
    
    private static void loginToAccount() {
        System.out.println("\n=== Login ===");
        
        System.out.print("Enter Account Number: ");
        String accountNumber = scanner.nextLine().trim();
        
        BankAccount account = accounts.get(accountNumber);
        if (account == null) {
            System.out.println("Account not found.");
            return;
        }
        
        if (account.isLocked()) {
            System.out.println("Your account is locked. Please reset your password using security question.");
            handlePasswordReset(account);
            return;
        }
        
        System.out.print("Enter Password: ");
        String password = scanner.nextLine();
        
        System.out.print("Enter PIN: ");
        String pin = scanner.nextLine().trim();
        
        if (account.getPassword().equals(password) && account.getPin().equals(pin)) {
            account.resetFailedLoginAttempts();
            System.out.println("Login successful! Welcome, " + account.getFullName());
            showAccountMenu(account);
        } else {
            System.out.println("Invalid credentials.");
            account.recordFailedLoginAttempt();
        }
    }
    
    private static void handlePasswordReset(BankAccount account) {
        System.out.println("\n=== Password Reset ===");
        System.out.println("Security Question: " + account.getSecurityQuestion());
        System.out.print("Enter your answer: ");
        String answer = scanner.nextLine().trim();
        
        System.out.print("Enter new password: ");
        String newPassword = scanner.nextLine();
        
        if (account.resetPassword(answer, newPassword)) {
            System.out.println("You can now login with your new password.");
        }
    }
    
    private static void showTotalAccounts() {
        System.out.println("\n=== Total Accounts Information ===");
        System.out.println("Total Accounts Created: " + totalAccountsCreated);
        
        int savingsCount = 0;
        int currentCount = 0;
        
        for (BankAccount account : accounts.values()) {
            if (account.getAccountType().equals("Savings")) {
                savingsCount++;
            } else {
                currentCount++;
            }
        }
        
        System.out.println("Savings Accounts: " + savingsCount);
        System.out.println("Current Accounts: " + currentCount);
    }
    
    private static int getIntInput(String prompt) {
        while (true) {
            try {
                System.out.print(prompt);
                return Integer.parseInt(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Please enter a valid number.");
            }
        }
    }
    
    private static double getDoubleInput(String prompt) {
        while (true) {
            try {
                System.out.print(prompt);
                return Double.parseDouble(scanner.nextLine().trim());
            } catch (NumberFormatException e) {
                System.out.println("Please enter a valid amount.");
            }
        }
    }

    private static void showAccountMenu(BankAccount account) {
        while (true) {
            System.out.println("\n=== Account Menu ===");
            System.out.println("1. Deposit");
            System.out.println("2. Withdraw");
            System.out.println("3. Check Balance");
            System.out.println("4. Change Password");
            System.out.println("5. Change PIN");
            System.out.println("6. View Account Details");
            System.out.println("7. Show Last 5 Transactions");
            if (account instanceof SavingsAccount) {
                System.out.println("8. Apply Interest (Savings only)");
            }
            System.out.println("9. Exit");

            int choice = getIntInput("Enter your choice: ");

            switch (choice) {
                case 1:
                    handleDeposit(account);
                    break;
                case 2:
                    handleWithdraw(account);
                    break;
                case 3:
                    handleCheckBalance(account);
                    break;
                case 4:
                    handleChangePassword(account);
                    break;
                case 5:
                    handleChangePin(account);
                    break;
                case 6:
                    account.viewAccountDetails();
                    break;
                case 7:
                    account.showLast5Transactions();
                    break;
                case 8:
                    if (account instanceof SavingsAccount) {
                        ((SavingsAccount) account).applyInterest();
                    } else {
                        System.out.println("Invalid choice.");
                    }
                    break;
                case 9:
                    System.out.println("Thank you for using our services!");
                    return;
                default:
                    System.out.println("Invalid choice. Please try again.");
            }
        }
    }

    private static void handleDeposit(BankAccount account) {
        System.out.println("\n=== Deposit ===");
        double amount = getDoubleInput("Enter amount to deposit: ₹");

        if (amount <= 0) {
            System.out.println("Amount must be positive.");
            return;
        }

        account.deposit(amount);
    }

    private static void handleWithdraw(BankAccount account) {
        System.out.println("\n=== Withdraw ===");
        double amount = getDoubleInput("Enter amount to withdraw: ₹");

        if (amount <= 0) {
            System.out.println("Amount must be positive.");
            return;
        }

        account.withdraw(amount);
    }

    private static void handleCheckBalance(BankAccount account) {
        System.out.println("\n=== Balance Inquiry ===");
        System.out.println("Current Balance: ₹" + String.format("%.2f", account.checkBalance()));

        if (account instanceof CurrentAccount) {
            CurrentAccount currentAcc = (CurrentAccount) account;
            System.out.println("Overdraft Limit: ₹" + currentAcc.getOverdraftLimit());
            System.out.println("Available Overdraft: ₹" + currentAcc.getAvailableOverdraft());
            double totalAvailable = account.getBalance() + currentAcc.getAvailableOverdraft();
            System.out.println("Total Available: ₹" + String.format("%.2f", totalAvailable));
        }
    }

    private static void handleChangePassword(BankAccount account) {
        System.out.println("\n=== Change Password ===");
        System.out.print("Enter current password: ");
        String oldPassword = scanner.nextLine();

        System.out.print("Enter new password: ");
        String newPassword = scanner.nextLine();

        account.changePassword(oldPassword, newPassword);
    }

    private static void handleChangePin(BankAccount account) {
        System.out.println("\n=== Change PIN ===");
        System.out.print("Enter current PIN: ");
        String oldPin = scanner.nextLine().trim();

        System.out.print("Enter new PIN: ");
        String newPin = scanner.nextLine().trim();

        account.changePin(oldPin, newPin);
    }
}
