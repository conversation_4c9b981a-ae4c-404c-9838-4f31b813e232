import java.time.LocalDate;

/**
 * Test class to demonstrate banking system functionality
 */
public class TestBankingSystem {
    public static void main(String[] args) {
        System.out.println("=== Banking System Test Demo ===\n");
        
        // Test Address creation
        System.out.println("1. Testing Address Creation:");
        Address address = new Address("123 Main Street", "Mumbai", "Maharashtra", "400001");
        System.out.println("Address: " + address.toString());
        System.out.println("Valid pincode test: " + Address.isValidPincode("400001"));
        System.out.println("Invalid pincode test: " + Address.isValidPincode("12345"));
        System.out.println();
        
        // Test Savings Account creation
        System.out.println("2. Testing Savings Account Creation:");
        SavingsAccount savingsAccount = new SavingsAccount(
            "**********", 
            "Nikhil Sharma", 
            "M", 
            LocalDate.of(1990, 8, 15),
            address,
            "Password123!",
            "5678",
            "What is your mother's maiden name?",
            "<PERSON>"
        );
        
        System.out.println("Savings account created successfully!");
        savingsAccount.viewAccountDetails();
        System.out.println();
        
        // Test Current Account creation
        System.out.println("3. Testing Current Account Creation:");
        CurrentAccount currentAccount = new CurrentAccount(
            "**********", 
            "Priya Singh", 
            "F", 
            LocalDate.of(1985, 12, 10),
            new Address("456 Business Park", "Delhi", "Delhi", "110001"),
            "SecurePass456@",
            "9876",
            "What was your first school?",
            "DPS"
        );
        
        System.out.println("Current account created successfully!");
        currentAccount.viewAccountDetails();
        System.out.println();
        
        // Test deposit operations
        System.out.println("4. Testing Deposit Operations:");
        savingsAccount.deposit(5000, "Initial deposit");
        currentAccount.deposit(10000, "Business deposit");
        System.out.println();
        
        // Test withdrawal operations
        System.out.println("5. Testing Withdrawal Operations:");
        System.out.println("--- Savings Account Withdrawal ---");
        savingsAccount.withdraw(2000); // Should work
        savingsAccount.withdraw(4000); // Should fail due to minimum balance
        
        System.out.println("\n--- Current Account Withdrawal ---");
        currentAccount.withdraw(5000); // Should work
        currentAccount.withdraw(15000); // Should work with overdraft
        System.out.println();
        
        // Test balance checking
        System.out.println("6. Testing Balance Checking:");
        System.out.println("Savings Account Balance: ₹" + savingsAccount.checkBalance());
        System.out.println("Current Account Balance: ₹" + currentAccount.checkBalance());
        if (currentAccount.isInOverdraft()) {
            System.out.println("Current Account Overdraft Used: ₹" + currentAccount.getOverdraftUsed());
        }
        System.out.println();
        
        // Test interest application
        System.out.println("7. Testing Interest Application (Savings Only):");
        savingsAccount.applyInterest();
        System.out.println();
        
        // Test password validation
        System.out.println("8. Testing Password Validation:");
        System.out.println("Valid password test: " + BankAccount.isValidPassword("Password123!"));
        System.out.println("Invalid password test: " + BankAccount.isValidPassword("weak"));
        System.out.println();
        
        // Test PIN validation
        System.out.println("9. Testing PIN Validation:");
        System.out.println("Valid PIN test: " + savingsAccount.isValidPin("1234"));
        System.out.println("Invalid PIN (related to DOB): " + savingsAccount.isValidPin("1508")); // DOB: 15/08
        System.out.println();
        
        // Test transaction history
        System.out.println("10. Testing Transaction History:");
        System.out.println("--- Savings Account Transactions ---");
        savingsAccount.showLast5Transactions();
        
        System.out.println("\n--- Current Account Transactions ---");
        currentAccount.showLast5Transactions();
        System.out.println();
        
        // Test failed login attempts
        System.out.println("11. Testing Failed Login Attempts:");
        savingsAccount.recordFailedLoginAttempt();
        savingsAccount.recordFailedLoginAttempt();
        savingsAccount.recordFailedLoginAttempt(); // This should lock the account
        System.out.println("Account locked status: " + savingsAccount.isLocked());
        System.out.println();
        
        // Test password reset
        System.out.println("12. Testing Password Reset:");
        boolean resetSuccess = savingsAccount.resetPassword("Gupta", "NewPassword456!");
        System.out.println("Password reset successful: " + resetSuccess);
        System.out.println("Account locked status after reset: " + savingsAccount.isLocked());
        System.out.println();
        
        // Test account details view
        System.out.println("13. Final Account Details:");
        System.out.println("--- Savings Account ---");
        savingsAccount.viewAccountDetails();
        
        System.out.println("\n--- Current Account ---");
        currentAccount.viewAccountDetails();
        
        System.out.println("\n=== Test Demo Completed Successfully! ===");
    }
}
